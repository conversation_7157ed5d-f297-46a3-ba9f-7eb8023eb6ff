// content script
console.log("📦 Content script loaded");

// 创建状态显示窗口
function createStatusWindow() {
  // 检查是否已存在状态窗口
  if (document.getElementById('amazon-plugin-status')) {
    return;
  }

  const statusWindow = document.createElement('div');
  statusWindow.id = 'amazon-plugin-status';
  statusWindow.innerHTML = `
    <div style="
      position: fixed;
      top: 20px;
      right: 20px;
      width: 350px;
      max-height: 400px;
      background: #fff;
      border: 2px solid #007cba;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      z-index: 10000;
      font-family: Arial, sans-serif;
      font-size: 12px;
    ">
      <div style="
        background: #007cba;
        color: white;
        padding: 8px 12px;
        border-radius: 6px 6px 0 0;
        font-weight: bold;
        display: flex;
        justify-content: space-between;
        align-items: center;
      ">
        <span>🤖 Amazon 插件运行状态</span>
        <button id="close-status-window" style="
          background: none;
          border: none;
          color: white;
          font-size: 16px;
          cursor: pointer;
          padding: 0;
          width: 20px;
          height: 20px;
        ">×</button>
      </div>
      <div id="status-content" style="
        padding: 12px;
        max-height: 320px;
        overflow-y: auto;
      ">
        <div id="progress-info" style="margin-bottom: 10px;">
          <div style="font-weight: bold; color: #333;">📊 处理进度</div>
          <div id="progress-text">等待开始...</div>
          <div style="background: #f0f0f0; height: 6px; border-radius: 3px; margin: 5px 0;">
            <div id="progress-bar" style="background: #007cba; height: 100%; border-radius: 3px; width: 0%; transition: width 0.3s;"></div>
          </div>
        </div>
        <div id="current-action" style="margin-bottom: 10px;">
          <div style="font-weight: bold; color: #333;">🔄 当前操作</div>
          <div id="action-text">准备中...</div>
        </div>
        <div id="log-section">
          <div style="font-weight: bold; color: #333;">📝 运行日志</div>
          <div id="log-content" style="
            background: #f8f8f8;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 8px;
            max-height: 150px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 11px;
            line-height: 1.4;
          ">
            插件已加载，等待开始处理...
          </div>
        </div>
      </div>
    </div>
  `;

  document.body.appendChild(statusWindow);

  // 添加关闭按钮事件
  document.getElementById('close-status-window').addEventListener('click', () => {
    statusWindow.remove();
  });

  // 使窗口可拖拽
  let isDragging = false;
  let currentX;
  let currentY;
  let initialX;
  let initialY;
  let xOffset = 0;
  let yOffset = 0;

  const header = statusWindow.querySelector('div');
  header.style.cursor = 'move';

  header.addEventListener('mousedown', (e) => {
    if (e.target.id === 'close-status-window') return;

    initialX = e.clientX - xOffset;
    initialY = e.clientY - yOffset;

    if (e.target === header || header.contains(e.target)) {
      isDragging = true;
    }
  });

  document.addEventListener('mousemove', (e) => {
    if (isDragging) {
      e.preventDefault();
      currentX = e.clientX - initialX;
      currentY = e.clientY - initialY;

      xOffset = currentX;
      yOffset = currentY;

      statusWindow.style.transform = `translate(${currentX}px, ${currentY}px)`;
    }
  });

  document.addEventListener('mouseup', () => {
    initialX = currentX;
    initialY = currentY;
    isDragging = false;
  });
}

// 更新状态窗口内容
function updateStatusWindow(type, data) {
  const progressText = document.getElementById('progress-text');
  const progressBar = document.getElementById('progress-bar');
  const actionText = document.getElementById('action-text');
  const logContent = document.getElementById('log-content');

  if (!progressText || !progressBar || !actionText || !logContent) {
    return;
  }

  const timestamp = new Date().toLocaleTimeString();

  switch (type) {
    case 'progress':
      const percentage = totalNum > 0 ? Math.round((totalProgress / totalNum) * 100) : 0;
      progressText.textContent = `${totalProgress}/${totalNum} (${percentage}%)`;
      progressBar.style.width = `${percentage}%`;
      break;

    case 'action':
      actionText.textContent = data;
      break;

    case 'log':
      const logEntry = document.createElement('div');
      logEntry.style.marginBottom = '2px';
      logEntry.innerHTML = `<span style="color: #666;">[${timestamp}]</span> ${data}`;
      logContent.appendChild(logEntry);
      logContent.scrollTop = logContent.scrollHeight;

      // 限制日志条数，避免内存占用过多
      const logEntries = logContent.children;
      if (logEntries.length > 50) {
        logContent.removeChild(logEntries[0]);
      }
      break;

    case 'total':
      totalNum = data;
      updateStatusWindow('progress', null);
      break;
  }
}

// 注入拦截器脚本
const script = document.createElement('script');
script.src = chrome.runtime.getURL('js/fileInterceptor.js');

// 安全地添加到页面
if (document.head) {
  document.head.appendChild(script);
} else if (document.documentElement) {
  document.documentElement.appendChild(script);
} else {
  console.error('❌ Cannot find head or documentElement to inject script');
}

// 全局变量
let totalNum = 0;
let normalDataMap = {};
let clickedSkuList = [];
let currentAsins = [];
let totalProgress = 0;
let consecutiveFailedPages = 0;
const maxConsecutiveFailedPages = 3;
let sellerId = "";
let domain = "";
let antiCsrfToken = "";

// 获取域名
function getDomain() {
  return window.location.hostname;
}

// MD5加密函数
function md5Encrypt(text) {
  // 简单的哈希函数，实际项目中应该使用crypto-js等库
  let hash = 0;
  if (text.length === 0) return hash.toString();
  for (let i = 0; i < text.length; i++) {
    const char = text.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return Math.abs(hash).toString();
}

// 时间转换函数
function convertUtcToCst(utcDateStr) {
  try {
    const date = new Date(utcDateStr);
    const cstDate = new Date(date.getTime() + 8 * 60 * 60 * 1000);
    return cstDate.toISOString().split('T')[0];
  } catch (e) {
    console.error('时间转换错误:', e);
    return utcDateStr;
  }
}

// 转换中文日期为时间戳
function convertChineseDateToTimestamp(dateStr) {
  if (!dateStr) return 0;
  
  try {
    if (dateStr.includes("年") && dateStr.includes("月") && dateStr.includes("日")) {
      const year = parseInt(dateStr.split("年")[0]);
      const month = parseInt(dateStr.split("年")[1].split("月")[0]);
      const day = parseInt(dateStr.split("月")[1].split("日")[0]);
      const date = new Date(year, month - 1, day);
      return Math.floor(date.getTime() / 1000);
    } else {
      const date = new Date(dateStr);
      return Math.floor(date.getTime() / 1000);
    }
  } catch (e) {
    console.error('时间格式化出错:', e);
    return dateStr;
  }
}

// 获取SKU和ASIN数量
function getSkuAsinCount(issueList) {
  const skus = new Set();
  const asins = new Set();
  
  issueList.forEach(issue => {
    const sku = issue.target?.artifactId;
    const asin = issue.parameters?.asin;
    if (sku) skus.add(sku);
    if (asin) asins.add(asin);
  });
  
  return { skus: skus.size, asins: asins.size };
}

// 发送API请求的通用函数
async function sendApiRequest(url, method, headers, body = null) {
  return new Promise((resolve) => {
    // 通过background script执行脚本注入
    chrome.runtime.sendMessage({
      type: 'EXECUTE_SCRIPT',
      url: url,
      method: method,
      headers: headers,
      body: body
    }, (response) => {
      if (chrome.runtime.lastError) {
        console.error('❌ Script injection failed:', chrome.runtime.lastError);
        resolve(null);
        return;
      }
      console.log('🔧 API响应:', response);
      // 检查是否有错误
      if (response && response.error) {
        console.error('❌ API请求错误:', response.error);
        resolve(null);
        return;
      }
      resolve(response);
    });
  });
}

// 获取政策详情
async function getPolicyDetail(policyEntityId) {
  const headers = {
    "authority": domain,
    "accept": "*/*",
    "accept-language": "zh-TW,zh;q=0.9",
    "anti-csrftoken-a2z-request": "true",
    "Content-Type": "application/json",
    "origin": `https://${domain}`,
    "referer": `https://${domain}/performance/account/health/product-policies?t=regulatory-compliance`,
    "sec-ch-ua": '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": '"Windows"',
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-origin",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
  };
  
  // 确保policyEntityId是数字类型
  const body = { "policyEntityId": parseInt(policyEntityId, 10) };
  const url = `https://${domain}/spx/myc/myc-backend-service/api/search-scpm-policies`;
  
  return await sendApiRequest(url, 'POST', headers, body);
}

// 获取提交批次信息
async function getArtifactSubmissionBatch(policyEntityId, resolutionPaths, sku) {
  const headers = {
    "authority": domain,
    "accept": "*/*",
    "accept-language": "zh-TW,zh;q=0.9",
    "anti-csrftoken-a2z-request": "true",
    "Content-Type": "application/json",
    "origin": `https://${domain}`,
    "referer": `https://${domain}/performance/account/health/product-policies?t=regulatory-compliance`,
    "sec-ch-ua": '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": '"Windows"',
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-origin",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
  };
  
  const body = {
    "contributor": {"ContributorType": "SELLER"},
    "policyId": parseInt(policyEntityId, 10),
    "resolutionPathId": resolutionPaths,
    "entity": {
      "grain": "SELLING_PARTNER_SKU_MARKETPLACE",
      "values": {"SKU": sku}
    },
    "artifactRequests": [{
      "namespace": "contribution",
      "name": "gpsr_safety_attestation",
      "schemaSource": "UMP"
    }]
  };
  
  const url = `https://${domain}/spx/myc/myc-backend-service/api/get-artifact-submission-batch`;
  
  return await sendApiRequest(url, 'POST', headers, body);
}

// 提交安全认证
async function submitSafetyAttestation(policyEntityId, resolutionPaths, sku) {
  const headers = {
    "authority": domain,
    "accept": "*/*",
    "accept-language": "en,en-GB;q=0.9",
    "anti-csrftoken-a2z": antiCsrfToken,
    "content-type": "application/json",
    "origin": `https://${domain}`,
    "referer": `https://${domain}/performance/account/health/product-policies?t=regulatory-compliance`,
    "sec-ch-ua": '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": '"Windows"',
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-origin",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
  };
  
  const body = {
    "contributor": {"ContributorType": "SELLER", "ContributorValue": sellerId},
    "policyId": parseInt(policyEntityId, 10),
    "resolutionPathId": resolutionPaths,
    "entity": {
      "grain": "SELLING_PARTNER_SKU_MARKETPLACE",
      "values": {"SKU": sku}
    },
    "artifacts": [{
      "namespace": "contribution",
      "name": "gpsr_safety_attestation",
      "schemaSource": "UMP",
      "payload": '{"value":true}',
      "selectors": {}
    }]
  };
  
  const url = `https://${domain}/spx/myc/myc-backend-service/api/put-artifact-submission-batch`;
  
  return await sendApiRequest(url, 'POST', headers, body);
}

// 处理单个缺陷项
async function processDefectItem(item, storeName, storeSite) {
  try {
    totalProgress += 1;

    // 更新进度
    updateStatusWindow('progress', null);
    
    const msgType = item.issueList?.[0]?.parameters?.message_id || "";
    const msg = normalDataMap[msgType] || "";
    
    // 只处理政策详情数据
    if (msgType !== "sp_health_detail_table_reason_psi_details") {
      console.log(`🚫 跳过非政策详情数据: ${msgType} (进度: ${totalProgress}/${totalNum})`);
      return;
    }
    
    const policyEntityId = item.issueList?.[0]?.parameters?.policy_id || "";
    const sku = item.issueList?.[0]?.target?.artifactId || "";
    const asin = item.issueList?.[0]?.parameters?.asin || "";
    const brand = item.issueList?.[0]?.parameters?.brand || "";
    const policyName = item.issueList?.[0]?.parameters?.policy_name || "";
    
    console.log(`📋 处理数据: policyEntityId: ${policyEntityId}, sku: ${sku}, msg: ${msg}`);

    // 更新当前操作状态
    updateStatusWindow('action', `处理 SKU: ${sku}`);
    updateStatusWindow('log', `📋 开始处理 SKU: ${sku}, ASIN: ${asin}`);
    
    // 检查是否已处理过
    if (clickedSkuList.includes(sku)) {
      console.log(`✅ 已处理过的SKU: ${sku}, 跳过`);
      return;
    }
    
    currentAsins.push(asin);
    
    // 时间处理
    const startTime = convertChineseDateToTimestamp(item.issueList?.[0]?.parameters?.impactDate);
    const dueTime = convertChineseDateToTimestamp(
      convertUtcToCst(item.issueList?.[0]?.parameters?.due_date)
    );
    
    // 获取SKU和ASIN数量
    const { skus, asins } = getSkuAsinCount(item.issueList);
    
    // 判断风险等级
    let hasProductSafetyCert = 0;
    if (policyName === "GPSR_Reactive_HighRisk_PSI_DE") {
      hasProductSafetyCert = 2;
    } else if (policyName === "GPSR_Reactive_MedLow-Risk_PSI_DE") {
      hasProductSafetyCert = 1;
    }
    
    // 生成唯一ID
    const currentTimestamp = Math.floor(Date.now() / 1000);
    const uniqueId = md5Encrypt(
      storeName + storeSite + msg + asin + sku + item.status + startTime + dueTime
    );
    
    console.log(`📊 任务进度: ${totalProgress}/${totalNum}`);
    console.log(`🏪 店铺: ${storeName}-${storeSite}`);
    console.log(`📝 消息: ${msg}`);
    console.log(`🔗 ASIN: ${asin}`);
    console.log(`📦 SKU: ${sku}`);
    console.log(`⚠️ 状态: ${item.status}`);
    console.log(`🔑 唯一ID: ${uniqueId}`);
    
    if (!sku || !policyEntityId) {
      console.error('❌ SKU或policyEntityId为空，跳过');
      return;
    }
    
    // 移除测试模式限制，处理所有数据
    
    // 获取政策详情
    console.log(`🔍 获取policyEntityId ${policyEntityId} 详情...`);
    updateStatusWindow('action', `获取政策详情: ${policyEntityId}`);
    updateStatusWindow('log', `🔍 获取政策详情...`);
    const detail = await getPolicyDetail(policyEntityId);
    console.log('🔧 政策详情响应:', detail);
    
    if (!detail || !detail.data) {
      console.error('❌检测不存在');
      console.error('❌ 获取政策详情失败');
      updateStatusWindow('log', `❌ 获取政策详情失败: ${policyEntityId}`);
      // 即使获取政策详情失败，也继续处理下一个项目
      return;
    }

    antiCsrfToken = detail.antiCsrfToken;
    console.log(`🎫 获取token成功: ${antiCsrfToken}`);
    updateStatusWindow('log', `🎫 获取token成功`);
    
    // 获取resolution paths
    let resolutionPaths = "";
    try {
      const resolutionConfig = detail.data.policies?.[0]?.policyAttributeNameToTypeValue?.resolutionConfig?.attributeValue;
      if (resolutionConfig) {
        const parsed = JSON.parse(resolutionConfig);
        resolutionPaths = parsed.ResolutionPaths?.[2]?.id || "";
      }
      console.log(`📍 Resolution paths: ${resolutionPaths}`);
    } catch (e) {
      console.error('❌ 获取resolutionConfig出错:', e);
    }
    
    // 等待随机时间
    await new Promise(resolve => setTimeout(resolve, Math.random() * 2000 + 1000));
    
    // 获取提交批次信息
    console.log(`📤 检查提交状态...`);
    updateStatusWindow('action', `检查提交状态: ${sku}`);
    updateStatusWindow('log', `📤 检查提交状态...`);
    const nextDetail = await getArtifactSubmissionBatch(policyEntityId, resolutionPaths, sku);
    console.log('🔧 提交批次响应:', nextDetail);
    
    if (!nextDetail) {
      console.log(`⚠️ 无法获取提交信息`);
      return;
    }
    
    antiCsrfToken = nextDetail.antiCsrfToken;
    console.log(`🎫 获取next_token成功: ${antiCsrfToken}`);
    
    // 准备数据库插入数据
    const insertData = {
      unique_id: uniqueId,
      platform_account: storeName,
      platform_site: storeSite,
      type: msg,
      asin: asin,
      sku: sku,
      status: item.status,
      platform_time: startTime,
      platform_end_time: dueTime,
      create_time: currentTimestamp,
      brand: brand,
      asin_num: asins,
      sku_num: skus,
      has_product_safety_cert: hasProductSafetyCert
    };
    
    // 等待随机时间
    await new Promise(resolve => setTimeout(resolve, Math.random() * 2000 + 1000));
    
    // 判断处理逻辑
    if (nextDetail.data === null && hasProductSafetyCert === 2) {
      // 高危无提交按钮
      console.log(`🚨 高危无提交按钮，直接记录`);
      updateStatusWindow('action', `高危无提交按钮: ${sku}`);
      updateStatusWindow('log', `🚨 高危无提交按钮，直接记录`);
      insertData.is_click = 2;
      insertData.data_status = 2;

      // 发送数据到background script
      chrome.runtime.sendMessage({
        type: 'DATABASE_INSERT',
        action: 'high_risk_no_submit',
        data: insertData
      });

    } else if (nextDetail.data && nextDetail.data.artifacts !== "") {
      // 未提交的数据，执行提交
      console.log(`📤 执行安全认证提交...`);
      updateStatusWindow('action', `执行提交: ${sku}`);
      updateStatusWindow('log', `📤 执行安全认证提交...`);
      const submitResult = await submitSafetyAttestation(policyEntityId, resolutionPaths, sku);

      if (submitResult) {
        console.log(`✅ 提交成功: ${JSON.stringify(submitResult.data)}`);
        updateStatusWindow('log', `✅ 提交成功: ${sku}`);
        clickedSkuList.push(sku);
        insertData.is_click = 1;

        // 发送数据到background script
        chrome.runtime.sendMessage({
          type: 'DATABASE_INSERT',
          action: 'submit_success',
          data: insertData,
          submitResult: submitResult.data
        });
      } else {
        console.error('❌ 提交失败');
        updateStatusWindow('log', `❌ 提交失败: ${sku}`);
      }

    } else {
      // 已提交的数据
      console.log(`✅ 已提交的数据，跳过`);
      updateStatusWindow('action', `已提交: ${sku}`);
      updateStatusWindow('log', `✅ 已提交的数据，跳过: ${sku}`);
      insertData.is_click = -1;

      // 发送数据到background script
      chrome.runtime.sendMessage({
        type: 'DATABASE_INSERT',
        action: 'already_submitted',
        data: insertData
      });
    }
    
    // 等待随机时间
    await new Promise(resolve => setTimeout(resolve, Math.random() * 1000 + 2000));
    
  } catch (error) {
    console.error('❌ 处理数据异常:', error);
  }
}

// 处理分页数据
async function processDefectsData(defects, storeName, storeSite) {
  if (!defects || defects.length === 0) {
    console.log('📭 没有产品政策数据');
    return { validDataCount: 0, skippedCount: 0 };
  }
  
  console.log('✅ 产品政策数据获取成功');
  console.log(`📊 当前数据量: ${defects.length}`);
  
  let validDataCount = 0;
  let skippedCount = 0;
  
  for (let i = 0; i < defects.length; i++) {
    const item = defects[i];
    const msgType = item.issueList?.[0]?.parameters?.message_id || "";
    
    if (msgType !== "sp_health_detail_table_reason_psi_details") {
      skippedCount++;
      continue;
    }
    
    validDataCount++;
    await processDefectItem(item, storeName, storeSite);
  }
  
  console.log(`📈 当前页面处理了${validDataCount}条有效数据，跳过了${skippedCount}条数据`);
  
  return { validDataCount, skippedCount };
}

// 点击下一页
async function clickNextPage() {
  return new Promise((resolve) => {
    // 通过background script执行脚本注入
    chrome.runtime.sendMessage({
      type: 'CLICK_NEXT_PAGE'
    }, (response) => {
      if (chrome.runtime.lastError) {
        console.error('❌ Click next page failed:', chrome.runtime.lastError);
        resolve(false);
        return;
      }
      resolve(response?.success || false);
    });
  });
}

// 等待页面加载
async function waitForPageLoad() {
  return new Promise((resolve) => {
    // 通过background script执行页面加载检查
    chrome.runtime.sendMessage({
      type: 'WAIT_FOR_PAGE_LOAD'
    }, (response) => {
      if (chrome.runtime.lastError) {
        console.error('❌ Wait for page load failed:', chrome.runtime.lastError);
        resolve(true); // 默认认为加载成功，避免中断流程
        return;
      }
      resolve(response?.loaded || true);
    });
  });
}

// 主处理逻辑
async function processDefectsPagination(responseData, storeName, storeSite) {
  const defects = responseData.defects || [];
  
  // 处理当前页数据
  const { validDataCount } = await processDefectsData(defects, storeName, storeSite);
  
  // 检查是否需要翻页
  if (defects.length === 25) {
    console.log('📄 尝试翻页...');
    updateStatusWindow('action', '尝试翻页...');
    updateStatusWindow('log', '📄 尝试翻页...');
    const clickResult = await clickNextPage();

    if (!clickResult) {
      consecutiveFailedPages++;
      console.warn(`⚠️ 翻页失败，连续失败次数: ${consecutiveFailedPages}`);
      updateStatusWindow('log', `⚠️ 翻页失败，连续失败次数: ${consecutiveFailedPages}`);

      if (consecutiveFailedPages >= maxConsecutiveFailedPages) {
        console.error(`❌ 连续${maxConsecutiveFailedPages}次翻页失败，停止处理`);
        updateStatusWindow('action', '翻页失败，停止处理');
        updateStatusWindow('log', `❌ 连续${maxConsecutiveFailedPages}次翻页失败，停止处理`);
        return false;
      }
    } else {
      consecutiveFailedPages = 0;
      console.log('✅ 翻页成功，等待页面加载');
      updateStatusWindow('log', '✅ 翻页成功，等待页面加载');

      // 验证页面是否正常加载
      const loadResult = await waitForPageLoad();
      if (!loadResult) {
        consecutiveFailedPages++;
        console.warn(`⚠️ 页面加载异常，连续失败次数: ${consecutiveFailedPages}`);
        updateStatusWindow('log', `⚠️ 页面加载异常，连续失败次数: ${consecutiveFailedPages}`);

        if (consecutiveFailedPages >= maxConsecutiveFailedPages) {
          console.error(`❌ 连续${maxConsecutiveFailedPages}次页面加载失败，停止处理`);
          updateStatusWindow('action', '页面加载失败，停止处理');
          updateStatusWindow('log', `❌ 连续${maxConsecutiveFailedPages}次页面加载失败，停止处理`);
          return false;
        }
      } else {
        console.log('✅ 页面加载成功，继续处理');
        updateStatusWindow('log', '✅ 页面加载成功，继续处理');
      }
    }
  } else {
    console.log('📄 当前页数据量不足25条，已到最后一页');
    updateStatusWindow('action', '处理完成');
    updateStatusWindow('log', '📄 当前页数据量不足25条，已到最后一页');
    return false; // 结束处理
  }
  
  return true; // 继续处理
}

// 监听来自fileInterceptor.js的消息
window.addEventListener('message', async (event) => {
  console.log('📨 Content script received window message:', event.data);
  
  if (event.source !== window) {
    console.log('❌ Ignoring message from different source');
    return;
  }
  
  if (event.data && event.data.source === 'amazon-api-interceptor') {
    domain = getDomain();
    
    // 获取store信息（需要从页面或其他地方获取）
    const storeName = "default_store"; // 需要实际获取
    const storeSite = "default_site";   // 需要实际获取
    
    const messageData = {
      type: event.data.type || 'AMAZON_API_RESPONSE',
      url: event.data.url,
      data: event.data.data
    };

    // 处理summary接口 - 获取总数据量
    if (event.data.type === 'summary') {
      console.log('📈 === SUMMARY 接口数据 ===');
      console.log('📡 URL:', event.data.url);

      totalNum = event.data.data?.listingLevelMetrics?.REGULATORY_COMPLIANCE?.defects?.count || 0;
      console.log(`📊 数据总量: ${totalNum}`);

      // 创建状态窗口并更新总数
      createStatusWindow();
      updateStatusWindow('total', totalNum);
      updateStatusWindow('log', `📊 获取到数据总量: ${totalNum}`);

      if (totalNum === 0) {
        console.log('📭 没有数据');
        updateStatusWindow('action', '没有数据需要处理');
        updateStatusWindow('log', '📭 没有数据需要处理');
        chrome.runtime.sendMessage({
          type: 'PROCESS_COMPLETE',
          message: '没有数据需要处理'
        });
        return;
      }

      messageData.totalCount = totalNum;
      
    } 
    // 处理zh-CN.json接口 - 获取本地化数据
    else if (event.data.url && event.data.url.includes("zh-CN.json")) {
      console.log('🌐 === 本地化数据 ===');
      normalDataMap = { ...normalDataMap, ...event.data.data };
      console.log('📋 本地化数据已更新');
    }
    // 处理defects-pagination接口 - 主要业务逻辑
    else if (event.data.type === 'defects-pagination') {
      console.log('🔍 === DEFECTS PAGINATION 接口数据 ===');
      console.log('📡 URL:', event.data.url);

      updateStatusWindow('log', `🔍 开始处理分页数据`);

      // 获取sellerId
      if (!sellerId && event.data.data.sellerId) {
        sellerId = event.data.data.sellerId;
        console.log(`🏪 获取到sellerId: ${sellerId}`);
        updateStatusWindow('log', `🏪 获取到sellerId: ${sellerId}`);
      }

      if (!sellerId) {
        console.error('❌ 未获取到sellerId');
        updateStatusWindow('log', `❌ 未获取到sellerId`);
        return;
      }

      // 处理分页数据
      await processDefectsPagination(event.data.data, storeName, storeSite);
    }
    else {
      console.log('❓ === 未知接口类型 ===');
      console.log('📡 URL:', event.data.url);
      console.log('📋 完整数据:', event.data.data);
    }

    console.log('📤 Sending message to background script:', messageData);
    
    // 发送消息到background script
    chrome.runtime.sendMessage(messageData, (response) => {
      if (chrome.runtime.lastError) {
        console.error('❌ Error sending message to background:', chrome.runtime.lastError);
      } else {
        console.log('✅ Message sent successfully, response:', response);
      }
    });
  }
});

console.log('✅ Content script fully loaded');
