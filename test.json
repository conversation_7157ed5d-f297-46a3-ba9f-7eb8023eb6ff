fetch('https://sellercentral.amazon.co.uk/spx/myc/myc-backend-service/api/put-artifact-submission-batch', {
    method: 'POST',
    headers: {
      'accept': '*/*',
      'accept-language': 'el',
      'anti-csrftoken-a2z': 'hBtOwWxNdhvg9JOHJQHOMtYz/3fcRRY0WQoq9ZZYa79FAAAAAGiMHFQ5MmQ2NTRhNy04ZjM3LTRlMmYtODIwNi01ZTYxMWY0YTVlZDU=',
      'content-type': 'application/json',
      'cookie': 'session-id=262-9650032-8933955; ubid-acbuk=262-6938778-6028102; __Host_mlang=zh_CN; i18n-prefs=GBP; sp-cdn="L5Z9:HK"; lc-acbuk=zh_CN; at-acbuk=Atza|IwEBIEwdtP5Y1Q8kgeWC07iEUbjAmaRcZ2cu-l9xmHDZrBfAtWABhAjxrMiBCvhyyZMZU9j7XjU2tfpL--8VUoMHLiJ4EmkPwOp5pJI-mqNda02LkHxjwnWu65kpJQ7ZEL-k62Nr2h1VyauFuoeOkNTmFDq7d4M5pUbI2KSj7REtCFPHLOs9nSAMD1nGo4LdAR_HZgA3MDtFVdQQ4MMhAfTPXdzElhR8KXONBxr6UEoNVZhK3mbSp12YyeW_u8s054Uu0eGUAzbv9348-nYKB0dG-r_Q3vpWrTfRx-g8KNDmS1gbyA; sess-at-acbuk="lUyDH/3dXMcDt0J8+AYhLxRsj1jAgIAWFAKhg/vPsRU="; sst-acbuk=Sst1|PQGv0U2UXt6oKhD4nzHh3v9GCTbcEWJotqgUfHT8TDLBWz4bXYV72oPtBqUJ0_QxbSL0QkxIOlLTZXOJGpC43CB6LQaLffFrRfSwdtKsH90tSHUoI9QlV4WlwGzWaAD8K5QzAKK6HArHZe0TuqCCi3kDFU_VytN1mMnjcv3oa4KXgpoi_1CtvEI5zpCBJOMD9CD-ShcKnfZEhtGxkB7RwoECpTFBnlsQ8yfjlAl4FdEqVnoC_6fSyll-JvnlKSfYszOahEMICotesoyWJIlvymD_BzD2znENu4lM-omroPmslJ0; csm-hit=tb:NF2J8PAGW5WTBRDPVE1A+s-ZD6SJ5DSSGNCDMBN28B5|1753789335974&t:1753789335974&adb:adblk_no; session-id-time=2082787201l; sid=wy8yXAv5rW43aGjb4iUAQQ==|Pp1A8JFn6iSIwLpOKigC1ujDLz/FTdd80BDIGIQJRhc=; __Host-mselc=H4sIAAAAAAAA/6tWSs5MUbJSSsytyjPUS0xOzi/NK9HLT85M0XM0DPHzsAgO847y8wkJUdJRykVSmZtalJyRCFKq52js5OPv6exi6eQV7hgGUpeNrLAApMTQOMzQ08k4zDMyKspDSUepAElFQSJIhZF/kJmvS5BvqLtRuIlSLQDtntf1mAAAAA==; x-acbuk=oJ4jSqZbVvN?EaWIbPi1?Hm@BehJDrLYC865iADe5zYdwzJTy0Jq1@BiutBay9?C; session-token=w50brSKK8dN2gTGMWvNpA2Jc8zgQtA/vO7pEmdd3pNYqmdbNBbf77crrlh2685e8iAu9C4jG6GAaBF1lIeIXkfYNQstVO/Aa6891EUPy7Ymz+NrCyn6975g0tJXJJ/7fn0lad2gKwDBJeg1Bbm1jFkv8lFdqOjGBBeOelG7VBaJgIxGae4Qe2LYL/JJzx7swnDvkWJzOxZppx08cVabO97n+UdbMlOF38uaKnYoIhfqr7mytiQnACy6paBfamrgvOBsxk+pdg603ha8Kqttpn6khN1Tzq2wLnM04GtckMlXRZM5veYMXbgvdHRg/98Q6IvLOeywgdxeGU4Hh+d700f6USaEvOra8OEk7bVj0ba0zVu0dRHnEGwdVP8BRctbORJSlxUKAjzs=; stck=EU; ph_phc_tGCcl9SINhy3N0zy98fdlWrc1ppQ67KJ8pZMzVZOECH_posthog=%7B%22distinct_id%22%3A%22amzn1.account.ocid.A1TNH8SVKZNLTT%22%2C%22%24sesid%22%3A%5B1754012867311%2C%**********-3982-70bc-b44b-75d0d9730bf3%22%2C1754010827138%5D%2C%22%24epp%22%3Atrue%7D',
      'origin': 'https://sellercentral.amazon.co.uk',
      'priority': 'u=1, i',
      'referer': 'https://sellercentral.amazon.co.uk/performance/account/health/product-policies?t=regulatory-compliance&script=1',
      'sec-ch-ua': '"Google Chrome";v="129", "Not=A?Brand";v="8", "Chromium";v="129"',
      'sec-ch-ua-mobile': '?0',
      'sec-ch-ua-platform': '"Windows"',
      'sec-fetch-dest': 'empty',
      'sec-fetch-mode': 'cors',
      'sec-fetch-site': 'same-origin',
      'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    },
    body: JSON.stringify({
      'contributor': {
        'ContributorType': 'SELLER',
        'ContributorValue': 'A3BLOICD9BJWAV'
      },
      'policyId': 185019,
      'resolutionPathId': '8bd96fcd-d136-4dfc-a99a-40a9e4e250eb',
      'entity': {
        'grain': 'SELLING_PARTNER_SKU_MARKETPLACE',
        'values': {
          'SKU': 'AFTL02-Ktm-11pm-932'
        }
      },
      'artifacts': [
        {
          'namespace': 'contribution',
          'name': 'gpsr_safety_attestation',
          'schemaSource': 'UMP',
          'payload': '{"value":true}',
          'selectors': {}
        }
      ],
      'marketplaceId': 'A13V1IB3VIYZZH'
    })
  });

  {
    "defects": [
        {
            "id": "V2_OTHER_A3BLOICD9BJWAV_A13V1IB3VIYZZH_B0FH5BD8NP_COSS.GPSR_PSI_ASIN",
            "alternateViolationId": "V2_OTHER_A3BLOICD9BJWAV_A13V1IB3VIYZZH_B0FH5BD8NP_COSS.GPSR_PSI_ASIN",
            "status": "Open",
            "title": "",
            "asinTitleMap": null,
            "imageURL": null,
            "asin": "B0FH5BD8NP",
            "federationFormatDate": "2025-07-30",
            "date": "2025年7月30日",
            "dueDate": null,
            "reasonMessage": null,
            "gated": null,
            "enforcementType": null,
            "appealId": null,
            "appealStatus": null,
            "appealToken": null,
            "appealEnforcementType": null,
            "appealRedirectUrl": null,
            "mobileRedirectUrl": null,
            "reinstatementPath": null,
            "appealForgivenessRejected": false,
            "caseId": null,
            "reason": null,
            "spaReasonCode": null,
            "appealsEmail": null,
            "removalTypeStringId": null,
            "brandName": null,
            "nextSteps": null,
            "documentClass": "",
            "ruleName": "",
            "sku": null,
            "enforced": false,
            "appealEnforcementId": "V2_OTHER_A3BLOICD9BJWAV_A13V1IB3VIYZZH_B0FH5BD8NP_COSS.GPSR_PSI_ASIN",
            "appealEnforcementNamespace": "stelr",
            "metricName": "REGULATORY_COMPLIANCE",
            "issueList": [
                {
                    "id": "issue.890573cd044ec7e533c3d0ba9da71292.ey31YUnNkP15rPcdmux1MAxz8HiTLNU5TWtCU2nahyg=_1753835764622",
                    "issueType": "COSS.GPSR_PSI_ASIN",
                    "issueHash": "ey31YUnNkP15rPcdmux1MAxz8HiTLNU5TWtCU2nahyg=",
                    "source": {
                        "target": "ASIN",
                        "artifactId": "B0FH5BD8NP"
                    },
                    "target": {
                        "target": "SKU",
                        "artifactId": "AFTL02-Ktm-11pm-932"
                    },
                    "parameters": {
                        "impactDate": "2025年7月30日",
                        "isHaulListing": false,
                        "policy_id": "185019",
                        "VIOLATION_LEVEL_GMS": "Unknown",
                        "LISTING_T12M_GMS_UNGROUP_SKU": "Unknown",
                        "due_date": "2024-12-13T00:00:00.000Z",
                        "policy_name": "GPSR_Reactive_MedLow-Risk_PSI_FR",
                        "message_id": "sp_health_detail_table_reason_psi_details",
                        "BASE_AHR_WEIGHT": "NONE",
                        "LISTING_T12M_GMS": "Unknown",
                        "violationGmsValue": 0,
                        "policy_platform": "CPP",
                        "programName": "ProgramNameNotFound",
                        "asin": "B0FH5BD8NP",
                        "due_date_localized": "2024年12月13日"
                    },
                    "impactDate": null,
                    "lastAssertedDate": null,
                    "marketplaceId": 5,
                    "exemption": null,
                    "enforcements": [],
                    "lastUpdatedTimestamp": 0,
                    "status": "Open"
                }
            ],
            "instanceType": "ASIN",
            "severity": "NONE"
        },
        {
            "id": "V2_OTHER_A3BLOICD9BJWAV_A13V1IB3VIYZZH_B0FH573L5Q_COSS.GPSR_PSI_ASIN",
            "alternateViolationId": "V2_OTHER_A3BLOICD9BJWAV_A13V1IB3VIYZZH_B0FH573L5Q_COSS.GPSR_PSI_ASIN",
            "status": "Open",
            "title": "",
            "asinTitleMap": null,
            "imageURL": null,
            "asin": "B0FH573L5Q",
            "federationFormatDate": "2025-07-29",
            "date": "2025年7月29日",
            "dueDate": null,
            "reasonMessage": null,
            "gated": null,
            "enforcementType": null,
            "appealId": null,
            "appealStatus": null,
            "appealToken": null,
            "appealEnforcementType": null,
            "appealRedirectUrl": null,
            "mobileRedirectUrl": null,
            "reinstatementPath": null,
            "appealForgivenessRejected": false,
            "caseId": null,
            "reason": null,
            "spaReasonCode": null,
            "appealsEmail": null,
            "removalTypeStringId": null,
            "brandName": null,
            "nextSteps": null,
            "documentClass": "",
            "ruleName": "",
            "sku": null,
            "enforced": false,
            "appealEnforcementId": "V2_OTHER_A3BLOICD9BJWAV_A13V1IB3VIYZZH_B0FH573L5Q_COSS.GPSR_PSI_ASIN",
            "appealEnforcementNamespace": "stelr",
            "metricName": "REGULATORY_COMPLIANCE",
            "issueList": [
                {
                    "id": "issue.ccd3bbac2619fb5fa8a8aac507c1bd7e.ey31YUnNkP15rPcdmux1MAxz8HiTLNU5TWtCU2nahyg=_1753814327884",
                    "issueType": "COSS.GPSR_PSI_ASIN",
                    "issueHash": "ey31YUnNkP15rPcdmux1MAxz8HiTLNU5TWtCU2nahyg=",
                    "source": {
                        "target": "ASIN",
                        "artifactId": "B0FH573L5Q"
                    },
                    "target": {
                        "target": "SKU",
                        "artifactId": "AFTL02-K2ss-14p-495"
                    },
                    "parameters": {
                        "impactDate": "2025年7月29日",
                        "isHaulListing": false,
                        "policy_id": "185019",
                        "VIOLATION_LEVEL_GMS": "Unknown",
                        "LISTING_T12M_GMS_UNGROUP_SKU": "Unknown",
                        "due_date": "2024-12-13T00:00:00.000Z",
                        "policy_name": "GPSR_Reactive_MedLow-Risk_PSI_FR",
                        "message_id": "sp_health_detail_table_reason_psi_details",
                        "BASE_AHR_WEIGHT": "NONE",
                        "LISTING_T12M_GMS": "Unknown",
                        "violationGmsValue": 0,
                        "policy_platform": "CPP",
                        "programName": "ProgramNameNotFound",
                        "asin": "B0FH573L5Q",
                        "due_date_localized": "2024年12月13日"
                    },
                    "impactDate": null,
                    "lastAssertedDate": null,
                    "marketplaceId": 5,
                    "exemption": null,
                    "enforcements": [],
                    "lastUpdatedTimestamp": 0,
                    "status": "Open"
                }
            ],
            "instanceType": "ASIN",
            "severity": "NONE"
        },
    "marketplaceId": "A13V1IB3VIYZZH",
    "sellerId": "A3BLOICD9BJWAV",
    "excludedMetrics": [],
    "startDate": "2025年2月3日",
    "endDate": "2025年8月1日",
    "nextPageToken": null,
    "contributorSystem": null,
    "vendorCodesList": null,
    "selectedVendorCode": null,
    "hasSupplementalInfo": false
}